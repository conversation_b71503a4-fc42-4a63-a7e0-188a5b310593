from unittest.mock import MagicMock, patch

import pytest
import respx
from sqlalchemy.orm import Session
from starlette.status import HTTP_200_OK, HTTP_404_NOT_FOUND


def test_delete_profile_nonexistent_studio_returns_404(
    db_session: Session,
    client,
    authorization_header,
):
    response = client.delete(
        "/studio/999999",  # Use a very high ID that's unlikely to exist
        headers=authorization_header,
    )

    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}


def test_delete_profile_success_with_mocked_powerbi_and_repo(
    db_session: Session,
    client,
    authorization_header,
):
    """Test successful deletion with fully mocked dependencies."""
    import uuid
    from datetime import datetime

    from dataset_manager.repo.profile import Profile

    # Create a real Profile object for the mock to return
    mock_profile_data = Profile(
        profile_id="test-profile-id-123",
        studio_id=42,
        profile_name="test_profile_42",
        active_version_id="2.0.4",
        permission_set_uuid=uuid.UUID("eeeeeee1-7d79-4403-83e9-916b65129739"),
        creation_timestamp=datetime.fromisoformat("2022-08-08T10:00:00"),
        deletion_timestamp=datetime.fromisoformat("2023-01-01T12:00:00"),
    )

    # Create another Profile object for the deleted profile (with deletion timestamp)
    deleted_profile_data = Profile(
        profile_id="test-profile-id-123",
        studio_id=42,
        profile_name="test_profile_42",
        active_version_id="2.0.4",
        permission_set_uuid=uuid.UUID("eeeeeee1-7d79-4403-83e9-916b65129739"),
        creation_timestamp=datetime.fromisoformat("2022-08-08T10:00:00"),
        deletion_timestamp=datetime.fromisoformat("2023-01-01T12:00:00"),
    )

    with (
        patch(
            "dataset_manager.api.studio.permission_set.ProfileRepo"
        ) as mock_repo_class,
        patch(
            "dataset_manager.api.studio.permission_set.delete"
        ) as mock_powerbi_delete,
    ):
        # Setup mock repository
        mock_repo = mock_repo_class.return_value
        mock_repo.get_for_studio.return_value = mock_profile_data
        mock_repo.delete.return_value = deleted_profile_data

        # Act: Call the DELETE endpoint
        response = client.delete(
            "/studio/42",
            headers=authorization_header,
        )

        # Assert: Verify response
        assert response.status_code == HTTP_200_OK

        # Verify the response contains the profile data
        response_data = response.json()
        assert response_data["studio_id"] == 42
        assert response_data["profile_id"] == "test-profile-id-123"
        assert response_data["deletion_timestamp"] == "2023-01-01T12:00:00"

        # Verify PowerBI delete was called with correct profile_id
        mock_powerbi_delete.assert_called_once_with("test-profile-id-123")

        # Verify repository methods were called correctly
        mock_repo.get_for_studio.assert_called_once_with(studio_id=42)
        mock_repo.delete.assert_called_once_with(profile=mock_profile_data)


def test_delete_profile_response_structure_validation(
    db_session: Session,
    client,
    authorization_header,
):
    """Test that the response structure matches the Profile model."""
    # Mock the ProfileRepo and PowerBI delete
    mock_profile = MagicMock()
    mock_profile.profile_id = "detailed-profile-id"
    mock_profile.studio_id = 100

    mock_deleted_profile = MagicMock()
    mock_deleted_profile.model_dump.return_value = {
        "profile_id": "detailed-profile-id",
        "profile_name": "test_profile_100",
        "studio_id": 100,
        "creation_timestamp": "2022-08-08T10:00:00",
        "deletion_timestamp": "2023-01-01T12:00:00",
        "active_version_id": "2.0.4",
        "permission_set_uuid": "eeeeeee1-7d79-4403-83e9-916b65129739",
    }

    with (
        patch(
            "dataset_manager.api.studio.permission_set.ProfileRepo"
        ) as mock_repo_class,
        patch(
            "dataset_manager.api.studio.permission_set.delete"
        ) as mock_powerbi_delete,
    ):
        mock_repo = mock_repo_class.return_value
        mock_repo.get_for_studio.return_value = mock_profile
        mock_repo.delete.return_value = mock_deleted_profile

        # Act: Call the DELETE endpoint
        response = client.delete(
            "/studio/100",
            headers=authorization_header,
        )

        # Assert: Verify response structure
        assert response.status_code == HTTP_200_OK
        response_data = response.json()

        # Check all expected fields are present
        expected_fields = [
            "profile_id",
            "profile_name",
            "studio_id",
            "creation_timestamp",
            "deletion_timestamp",
            "active_version_id",
            "permission_set_uuid",
        ]
        for field in expected_fields:
            assert field in response_data, f"Missing field: {field}"

        # Verify field values
        assert response_data["studio_id"] == 100
        assert response_data["profile_id"] == "detailed-profile-id"
        assert response_data["deletion_timestamp"] is not None


def test_delete_profile_with_different_studio_ids(
    db_session: Session,
    client,
    authorization_header,
):
    """Test deletion works correctly for different studio IDs."""
    test_cases = [
        {"studio_id": 0, "profile_id": "profile-0"},
        {"studio_id": 2147483647, "profile_id": "profile-max"},  # Max 32-bit integer
        {"studio_id": 12345, "profile_id": "profile-12345"},
    ]

    for case in test_cases:
        with (
            patch(
                "dataset_manager.api.studio.permission_set.ProfileRepo"
            ) as mock_repo_class,
            patch(
                "dataset_manager.api.studio.permission_set.delete"
            ) as mock_powerbi_delete,
        ):
            # Setup mock for this test case
            mock_profile = MagicMock()
            mock_profile.profile_id = case["profile_id"]
            mock_profile.studio_id = case["studio_id"]

            mock_deleted_profile = MagicMock()
            mock_deleted_profile.model_dump.return_value = {
                "profile_id": case["profile_id"],
                "studio_id": case["studio_id"],
                "deletion_timestamp": "2023-01-01T12:00:00",
            }

            mock_repo = mock_repo_class.return_value
            mock_repo.get_for_studio.return_value = mock_profile
            mock_repo.delete.return_value = mock_deleted_profile

            # Act: Delete the studio
            response = client.delete(
                f"/studio/{case['studio_id']}",
                headers=authorization_header,
            )

            # Assert: Verify success
            assert response.status_code == HTTP_200_OK
            assert response.json()["studio_id"] == case["studio_id"]
            mock_powerbi_delete.assert_called_once_with(case["profile_id"])


def test_delete_profile_powerbi_api_failure_handling(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test that local deletion proceeds even if PowerBI API call fails."""
    # Arrange: Create a profile in the database
    profile = profile_factory(studio_id=1, profile_id="test-profile-id-456")

    # Mock PowerBI delete API to return an error
    powerbi_delete_mock = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/test-profile-id-456"
    ).respond(status_code=500, json={"error": "Internal server error"})

    # Act: Call the DELETE endpoint
    response = client.delete(
        "/studio/1",
        headers=authorization_header,
    )

    # Assert: The endpoint should still succeed (local deletion happens after PowerBI call)
    # Note: The actual behavior depends on implementation - if PowerBI call fails,
    # the endpoint might return an error. This test documents the expected behavior.
    assert response.status_code == HTTP_200_OK

    # Verify PowerBI delete API was called
    assert powerbi_delete_mock.called


def test_delete_profile_with_different_studio_ids(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test deletion works correctly for different studio IDs."""
    # Arrange: Create profiles for different studios
    profile1 = profile_factory(studio_id=100, profile_id="profile-100")
    profile2 = profile_factory(studio_id=200, profile_id="profile-200")

    # Mock PowerBI delete API calls
    powerbi_delete_mock_100 = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/profile-100"
    ).respond(status_code=200)

    powerbi_delete_mock_200 = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/profile-200"
    ).respond(status_code=200)

    # Act & Assert: Delete first studio
    response1 = client.delete("/studio/100", headers=authorization_header)
    assert response1.status_code == HTTP_200_OK
    assert response1.json()["studio_id"] == 100
    assert powerbi_delete_mock_100.called

    # Act & Assert: Delete second studio
    response2 = client.delete("/studio/200", headers=authorization_header)
    assert response2.status_code == HTTP_200_OK
    assert response2.json()["studio_id"] == 200
    assert powerbi_delete_mock_200.called

    # Verify both API calls were made
    assert len(respx_mock.calls) == 2


def test_delete_profile_already_deleted_studio_returns_404(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test that trying to delete an already deleted studio returns 404."""
    # Arrange: Create a profile and mark it as deleted
    from datetime import datetime

    profile = profile_factory(studio_id=1, profile_id="deleted-profile")

    # Manually set deletion timestamp to simulate already deleted profile
    from dataset_manager.repo.profile import _DBProfile

    db_profile = db_session.query(_DBProfile).filter_by(studio_id=1).first()
    db_profile.deletion_timestamp = datetime.utcnow()
    db_session.commit()

    # Act: Try to delete the already deleted studio
    response = client.delete(
        "/studio/1",
        headers=authorization_header,
    )

    # Assert: Should return 404 since profile is already marked as deleted
    assert response.status_code == HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Studio not found"}

    # Verify no PowerBI API calls were made
    assert len(respx_mock.calls) == 0


@pytest.fixture
def mock_powerbi_profiles_delete():
    """Mock the PowerBI profiles delete function to avoid external API calls in unit tests."""
    with patch("dataset_manager.api.studio.permission_set.delete") as mock_delete:
        yield mock_delete


def test_delete_profile_unit_test_with_mocked_powerbi(
    db_session: Session,
    client,
    authorization_header,
    existing_customer,
    mock_powerbi_profiles_delete,
):
    """Unit test with mocked PowerBI delete to test the endpoint logic in isolation."""
    # Arrange: Use existing_customer fixture which creates a profile with studio_id=1
    # The existing_customer fixture creates all necessary data including profile

    # Act: Call the DELETE endpoint
    response = client.delete(
        "/studio/1",
        headers=authorization_header,
    )

    # Assert: Verify response
    assert response.status_code == HTTP_200_OK
    response_data = response.json()
    assert response_data["studio_id"] == 1
    assert response_data["profile_id"] is not None

    # Verify PowerBI delete was called with the profile_id
    mock_powerbi_profiles_delete.assert_called_once()


def test_delete_profile_response_structure_matches_profile_model(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    mock_powerbi_profiles_delete,
):
    """Test that the response structure matches the Profile model."""
    # Arrange: Create a profile with all fields
    profile = profile_factory(
        studio_id=42,
        profile_id="detailed-profile-id",
        profile_name="test_profile_42",
        active_version_id="2.0.4",
        permission_set_uuid="eeeeeee1-7d79-4403-83e9-916b65129739",
    )
    db_session.commit()  # Ensure the profile is committed to the database

    # Act: Call the DELETE endpoint
    response = client.delete(
        "/studio/42",
        headers=authorization_header,
    )

    # Assert: Verify response structure
    assert response.status_code == HTTP_200_OK
    response_data = response.json()

    # Check all expected fields are present
    expected_fields = [
        "profile_id",
        "profile_name",
        "studio_id",
        "creation_timestamp",
        "deletion_timestamp",
        "active_version_id",
        "permission_set_uuid",
    ]
    for field in expected_fields:
        assert field in response_data, f"Missing field: {field}"

    # Verify field values
    assert response_data["studio_id"] == 42
    assert response_data["profile_id"] == "detailed-profile-id"
    assert response_data["profile_name"] == "test_profile_42"
    assert response_data["active_version_id"] == "2.0.4"
    assert (
        response_data["permission_set_uuid"] == "eeeeeee1-7d79-4403-83e9-916b65129739"
    )
    assert response_data["deletion_timestamp"] is not None


def test_delete_profile_with_zero_studio_id(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test deletion with studio_id = 0 (edge case)."""
    # Arrange: Create a profile with studio_id = 0
    profile = profile_factory(studio_id=0, profile_id="zero-studio-profile")

    # Mock PowerBI delete API call
    powerbi_delete_mock = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/zero-studio-profile"
    ).respond(status_code=200)

    # Act: Call the DELETE endpoint
    response = client.delete(
        "/studio/0",
        headers=authorization_header,
    )

    # Assert: Should work normally
    assert response.status_code == HTTP_200_OK
    assert response.json()["studio_id"] == 0
    assert powerbi_delete_mock.called


def test_delete_profile_with_large_studio_id(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test deletion with a very large studio_id."""
    # Arrange: Create a profile with large studio_id
    large_studio_id = 2147483647  # Max 32-bit integer
    profile = profile_factory(
        studio_id=large_studio_id, profile_id="large-studio-profile"
    )

    # Mock PowerBI delete API call
    powerbi_delete_mock = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/large-studio-profile"
    ).respond(status_code=200)

    # Act: Call the DELETE endpoint
    response = client.delete(
        f"/studio/{large_studio_id}",
        headers=authorization_header,
    )

    # Assert: Should work normally
    assert response.status_code == HTTP_200_OK
    assert response.json()["studio_id"] == large_studio_id
    assert powerbi_delete_mock.called


def test_delete_profile_powerbi_timeout_handling(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    respx_mock: respx.MockRouter,
):
    """Test handling of PowerBI API timeout."""
    # Arrange: Create a profile
    profile = profile_factory(studio_id=1, profile_id="timeout-test-profile")

    # Mock PowerBI delete API to simulate timeout
    import httpx

    powerbi_delete_mock = respx_mock.delete(
        url="https://api.powerbi.com/v1.0/myorg/profiles/timeout-test-profile"
    ).mock(side_effect=httpx.TimeoutException("Request timeout"))

    # Act: Call the DELETE endpoint
    response = client.delete(
        "/studio/1",
        headers=authorization_header,
    )

    # Assert: Should handle timeout gracefully
    # Note: The actual behavior depends on implementation
    # This test documents expected behavior for timeout scenarios
    assert response.status_code in [
        HTTP_200_OK,
        503,
    ]  # Either success or service unavailable
    assert powerbi_delete_mock.called


def test_delete_profile_concurrent_deletion_safety(
    db_session: Session,
    client,
    authorization_header,
    profile_factory,
    mock_powerbi_profiles_delete,
):
    """Test that concurrent deletion attempts are handled safely."""
    # Arrange: Create a profile
    profile = profile_factory(studio_id=1, profile_id="concurrent-test-profile")

    # Act: Make multiple concurrent deletion requests
    # Note: In a real concurrent test, you'd use threading or asyncio
    # This is a simplified version to test the basic logic
    response1 = client.delete("/studio/1", headers=authorization_header)
    response2 = client.delete("/studio/1", headers=authorization_header)

    # Assert: First should succeed, second should return 404
    assert response1.status_code == HTTP_200_OK
    assert response2.status_code == HTTP_404_NOT_FOUND
    assert response2.json() == {"detail": "Studio not found"}
